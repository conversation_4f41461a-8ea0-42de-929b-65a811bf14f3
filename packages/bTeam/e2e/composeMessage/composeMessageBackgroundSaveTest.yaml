# Test background save functionality in compose message
# Open Compose email, insert attachment, recipient, subject & body and do not save
# Put app in background (or kill app), re-open the app, go to Drafts folder and open the email again
# Expected: The data should be automatically saved when app goes to background and visible when reopening
# This test handles both background save and app kill scenarios with local storage backup

# composeMessageBackgroundSaveTest.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- clearState
- launchApp
- tapOn:
    id: "inbox--composeIcon"

# Select From field
- tapOn:
    id: "compose__fields--from"
- tapOn: "W, <EMAIL>"
- tapOn:
    id: "selectRecipientsBottomSheet--addButton"

# Fill To field
- tapOn:
    id: "compose__fields--to"
- inputText: "<EMAIL>"

# Fill Subject field
- tapOn:
    id: "compose__fields--subject"
- inputText: "Background Save Test Subject"

# Fill Body field
- tapOn:
    id: "compose__fields--body"
- inputText: "This is a test message for background save functionality. The content should be preserved when the app goes to background."

# Expand fields and add CC
- tapOn:
    id: "compose--expandFieldsButton"
- tapOn:
    id: "compose__fields--cc"
- inputText: "<EMAIL>"

# Add BCC
- tapOn:
    id: "compose__fields--bcc"
- inputText: "<EMAIL>"

# Add attachment (if test.pdf exists)
- tapOn:
    id: "compose__header--addAttachmentButton"
- tapOn:
    text: "test.pdf"
    index: 0
- assertVisible:
    id: "compose__header--attachmentCount"
    text: 1

# Put app in background (this should trigger the background save)
- runScript: |
    const { exec } = require('child_process');
    exec('xcrun simctl spawn booted launchctl kickstart -k system/com.apple.SpringBoard');

# Wait a moment for the background save to complete
- wait: 2000

# Bring app back to foreground
- launchApp

# Navigate to Drafts folder
- tapOn: "Drafts"

# Look for the background-saved draft message
- assertVisible: "Background Save Test Subject"

# Open the draft message
- tapOn: "Background Save Test Subject"

# Verify all fields are preserved
- assertVisible: "<EMAIL>"
- assertVisible: "Background Save Test Subject"
- assertVisible: "This is a test message for background save functionality"
- assertVisible: "<EMAIL>"
- assertVisible: "<EMAIL>"

# Verify attachment is preserved
- assertVisible:
    id: "compose__header--attachmentCount"
    text: 1

# Go back to inbox
- tapOn:
    id: "compose__header--closeButton"
- tapOn: "DISCARD"
- tapOn:
    id: "inbox__header--burgerIcon"
