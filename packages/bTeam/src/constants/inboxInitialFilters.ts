import {
  DATE_PERIOD_OPTIONS,
  FLAGGED_OPTIONS,
  HAS_ATTACHMENTS_OPTIONS,
  IN_OUT_OPTIONS,
  READ_UNREAD_OPTIONS,
  SEARCH_FIELD_NAMES,
  SEARCH_IN_OPTIONS,
} from "./gridMessagesSearchFields";

export const INBOX_INITIAL_FILTERS = {
  [SEARCH_FIELD_NAMES.searchIn]: `${SEARCH_IN_OPTIONS.subject.value},${SEARCH_IN_OPTIONS.body.value}`,
  [SEARCH_FIELD_NAMES.inOut]: IN_OUT_OPTIONS.notSet.value,
  [SEARCH_FIELD_NAMES.from]: "",
  [SEARCH_FIELD_NAMES.to]: "",
  [SEARCH_FIELD_NAMES.datePeriod]: DATE_PERIOD_OPTIONS.oneMonth.value,
  [SEARCH_FIELD_NAMES.flagged]: FLAGGED_OPTIONS.notSet.value,
  [SEARCH_FIELD_NAMES.hasAttachments]: HAS_ATTACHMENTS_OPTIONS.notSet.value,
  [SEARCH_FIELD_NAMES.readUnread]: READ_UNREAD_OPTIONS.notSet.value,
};
