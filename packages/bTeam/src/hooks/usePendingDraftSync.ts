import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { syncPendingDraft, clearPendingDraft } from '../slices/generalSlice';

/**
 * Hook to automatically sync pending drafts when the app starts
 * This should be used in the main app component to handle pending drafts
 * that were saved when the app went to background or was killed
 */
export const usePendingDraftSync = () => {
  const dispatch = useDispatch();
  
  const pendingDraft = useSelector(
    (state: any) => state.root.bTeamGeneralSlice.pendingDraft
  );
  
  const token = useSelector(
    (state: any) => state.persist.bTeamAuth.token
  );

  useEffect(() => {
    // Only run once when the app starts and we have both token and pending draft
    if (token && pendingDraft && pendingDraft.timestamp) {
      // Check if the pending draft is recent (within last 24 hours)
      const isRecent = (Date.now() - pendingDraft.timestamp) < (24 * 60 * 60 * 1000);
      
      if (isRecent) {
        // Dispatch syncPendingDraft action to trigger the cycle
        dispatch(syncPendingDraft(pendingDraft));
      } else {
        // If too old, just clear it
        dispatch(clearPendingDraft());
      }
    }
  }, [token, pendingDraft, dispatch]);

  return {
    hasPendingDraft: !!pendingDraft,
    pendingDraftTimestamp: pendingDraft?.timestamp,
  };
};
