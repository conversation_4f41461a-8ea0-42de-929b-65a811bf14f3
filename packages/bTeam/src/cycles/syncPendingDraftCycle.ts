import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import delay from "xstream/extra/delay";
import {
  syncPendingDraft,
  sendDraftMessageSilent,
  clearPendingDraft,
} from "../slices/generalSlice.ts";

// Cycle to handle the syncPendingDraft action (triggered by dispatch)
export const handleSyncPendingDraft = (sources) => {
  const syncAction$ = sources.ACTION.filter(
    (action) => action.type === syncPendingDraft.type
  )

  const silentSaveAction$ = syncAction$.map(([action]) => {
    // Check if the pending draft is recent (within last 24 hours)
    const pendingDraft = action.payload;
    const isRecent = pendingDraft.timestamp &&
      (Date.now() - pendingDraft.timestamp) < (24 * 60 * 60 * 1000);

    if (isRecent) {
      // Convert sync action to silent save action
      return sendDraftMessageSilent(pendingDraft);
    }

    // If too old, just clear it
    return clearPendingDraft();
  });

  const clearAction$ = syncAction$.compose(delay(2000)).map(() => {
    // Clear pending draft after attempting to sync (give time for server request)
    return clearPendingDraft();
  });

  return {
    ACTION: xs.merge(silentSaveAction$, clearAction$),
  };
};
