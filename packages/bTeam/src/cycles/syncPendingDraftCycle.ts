import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import delay from "xstream/extra/delay";
import {
  syncPendingDraft,
  sendDraftMessageSilent,
  clearPendingDraft,
} from "../slices/generalSlice.ts";

// Cycle to sync pending drafts when app reopens
export const syncPendingDraftOnAppStart = (sources) => {
  const state$ = sources.STATE;

  const token$ = state$.map((state) => state?.persist?.bTeamAuth?.token);
  const pendingDraft$ = state$.map((state) => state.root.bTeamGeneralSlice.pendingDraft);

  // Combine token and pending draft availability
  const syncTrigger$ = xs.combine(token$, pendingDraft$)
    .map(([token, pendingDraft]) => {
      // Check if the pending draft is recent (within last 24 hours)
      const isRecent = pendingDraft.timestamp &&
        (Date.now() - pendingDraft.timestamp) < (24 * 60 * 60 * 1000);

      if (isRecent) {
        return syncPendingDraft(pendingDraft);
      }

      // If too old, just clear it
      return clearPendingDraft();
    });

  return {
    ACTION: syncTrigger$,
  };
};

// Cycle to handle the actual sync action
export const handleSyncPendingDraft = (sources) => {
  const syncAction$ = sources.ACTION.filter(
    (action) => action.type === syncPendingDraft.type
  );

  const silentSaveAction$ = syncAction$.map((action) => {
    // Convert sync action to silent save action
    return sendDraftMessageSilent(action.payload);
  });

  const clearAction$ = syncAction$.compose(delay(1000)).map(() => {

    // Clear pending draft after attempting to sync
    return clearPendingDraft();
  });

  return {
    ACTION: xs.merge(silentSaveAction$, clearAction$),
  };
};
