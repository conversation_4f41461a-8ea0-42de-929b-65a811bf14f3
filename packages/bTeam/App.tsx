import React from "react";
import { StyleSheet, View } from "react-native";
import RootNavigation from "./src/navigation/RootNavigation";
import { type Theme, useThemeAwareObject } from "b-ui-lib";
import { usePendingDraftSync } from "./src/hooks/usePendingDraftSync";

const App = () => {
  const { styles } = useThemeAwareObject(createStyles);

  // Initialize pending draft sync on app start
  usePendingDraftSync();

  return (
    <View style={styles.root}>
      <RootNavigation />
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    root: {
      flex: 1,
    },
  });

  return { styles, color };
};

export default App;
